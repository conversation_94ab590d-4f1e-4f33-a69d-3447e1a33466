{"name": "stackedit", "version": "5.15.4", "description": "Free, open-source, full-featured <PERSON><PERSON> editor", "author": "<PERSON><PERSON>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/benweet/stackedit/issues"}, "main": "index.js", "scripts": {"postinstall": "gulp build-prism", "start": "node build/dev-server.js", "build": "node build/build.js && npm run build-style", "build-style": "webpack --config build/webpack.style.conf.js", "lint": "eslint --ext .js,.vue src server", "unit": "jest --config test/unit/jest.conf.js --runInBand", "unit-with-coverage": "jest --config test/unit/jest.conf.js --runInBand --coverage", "test": "npm run lint && npm run unit", "preversion": "npm run test", "postversion": "git push origin master --tags && npm publish", "patch": "npm version patch -m \"Tag v%s\"", "minor": "npm version minor -m \"Tag v%s\"", "major": "npm version major -m \"Tag v%s\"", "chart": "mkdir -p dist && rm -rf dist/stackedit && cp -r chart dist/stackedit && sed -i.bak -e s/STACKEDIT_VERSION/$npm_package_version/g dist/stackedit/*.yaml && rm dist/stackedit/*.yaml.bak"}, "dependencies": {"@vue/test-utils": "^1.0.0-beta.16", "abcjs": "^5.2.0", "aws-sdk": "^2.1380.0", "babel-runtime": "^6.26.0", "bezier-easing": "^1.1.0", "body-parser": "^1.18.2", "clipboard": "^1.7.1", "compression": "^1.7.0", "diff-match-patch": "^1.0.0", "file-saver": "^1.3.8", "google-id-token-verifier": "^0.2.3", "handlebars": "^4.0.10", "indexeddbshim": "^3.6.2", "js-yaml": "^3.11.0", "katex": "^0.13.0", "markdown-it": "^8.4.1", "markdown-it-abbr": "^1.0.4", "markdown-it-deflist": "^2.0.2", "markdown-it-emoji": "^1.3.0", "markdown-it-footnote": "^3.0.1", "markdown-it-imsize": "^2.0.1", "markdown-it-mark": "^2.0.0", "markdown-it-pandoc-renderer": "1.2.0", "markdown-it-sub": "^1.0.0", "markdown-it-sup": "^1.0.0", "mermaid": "^8.9.2", "mousetrap": "^1.6.1", "normalize-scss": "^7.0.1", "prismjs": "^1.6.0", "request": "^2.85.0", "serve-static": "^1.13.2", "tmp": "^0.0.33", "turndown": "^4.0.2", "vue": "^2.5.16", "vuex": "^3.0.1"}, "devDependencies": {"autoprefixer": "^6.7.2", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "babel-jest": "^21.0.2", "babel-loader": "^7.1.4", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^1.1.3", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.5.1", "css-loader": "^0.28.11", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-webpack": "^0.9.0", "eslint-loader": "^2.0.0", "eslint-plugin-html": "^4.0.3", "eslint-plugin-import": "^2.11.0", "eventsource-polyfill": "^0.9.6", "express": "^4.16.3", "extract-text-webpack-plugin": "^2.0.0", "favicons-webpack-plugin": "^0.0.9", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.7.0", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "html-webpack-plugin": "^3.2.0", "http-proxy-middleware": "^0.18.0", "identity-obj-proxy": "^3.0.0", "ignore-loader": "^0.1.2", "jest": "^23.0.0", "jest-raw-loader": "^1.0.1", "jest-serializer-vue": "^0.3.0", "node-sass": "^4.0.0", "npm-bump": "^0.0.23", "offline-plugin": "^5.0.3", "opn": "^4.0.2", "optimize-css-assets-webpack-plugin": "^1.3.2", "ora": "^1.2.0", "raw-loader": "^0.5.1", "replace-in-file": "^4.1.0", "rimraf": "^2.6.0", "sass-loader": "^7.0.1", "semver": "^5.5.0", "shelljs": "^0.8.1", "string-replace-loader": "^2.1.1", "stylelint": "^9.2.0", "stylelint-config-standard": "^16.0.0", "stylelint-processor-html": "^1.0.0", "stylelint-webpack-plugin": "^0.10.4", "url-loader": "^1.0.1", "vue-jest": "^1.0.2", "vue-loader": "^15.0.9", "vue-style-loader": "^4.1.0", "vue-template-compiler": "^2.5.16", "webpack": "^2.6.1", "webpack-bundle-analyzer": "^3.3.2", "webpack-dev-middleware": "^1.10.0", "webpack-hot-middleware": "^2.18.0", "webpack-merge": "^4.1.2", "webpack-pwa-manifest": "^3.7.1", "worker-loader": "^1.1.1"}, "engines": {"node": ">= 8.0.0", "npm": ">= 5.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}