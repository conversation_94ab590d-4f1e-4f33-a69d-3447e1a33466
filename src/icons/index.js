import Vue from 'vue';
import Provider from './Provider';
import FormatBold from './FormatBold';
import FormatItalic from './FormatItalic';
import FormatQuoteClose from './FormatQuoteClose';
import LinkVariant from './LinkVariant';
import FileImage from './FileImage';
import Table from './Table';
import FormatListNumbers from './FormatListNumbers';
import FormatListBulleted from './FormatListBulleted';
import FormatSize from './FormatSize';
import FormatStrikethrough from './FormatStrikethrough';
import StatusBar from './StatusBar';
import NavigationBar from './NavigationBar';
import SidePreview from './SidePreview';
import Eye from './Eye';
import Settings from './Settings';
import FilePlus from './FilePlus';
import FileMultiple from './FileMultiple';
import FolderPlus from './FolderPlus';
import Delete from './Delete';
import Close from './Close';
import Pen from './Pen';
import Target from './Target';
import ArrowLeft from './ArrowLeft';
import HelpCircle from './HelpCircle';
import Toc from './Toc';
import Login from './Login';
import Logout from './Logout';
import Sync from './Sync';
import SyncOff from './SyncOff';
import Upload from './Upload';
import ViewList from './ViewList';
import Download from './Download';
import CodeTags from './CodeTags';
import CodeBraces from './CodeBraces';
import OpenInNew from './OpenInNew';
import Information from './Information';
import Alert from './Alert';
import SignalOff from './SignalOff';
import Folder from './Folder';
import ScrollSync from './ScrollSync';
import Printer from './Printer';
import Undo from './Undo';
import Redo from './Redo';
import ContentSave from './ContentSave';
import Message from './Message';
import History from './History';
import Database from './Database';
import Magnify from './Magnify';
import FormatListChecks from './FormatListChecks';
import CheckCircle from './CheckCircle';
import ContentCopy from './ContentCopy';
import Key from './Key';
import DotsHorizontal from './DotsHorizontal';
import Seal from './Seal';

Vue.component('iconProvider', Provider);
Vue.component('iconFormatBold', FormatBold);
Vue.component('iconFormatItalic', FormatItalic);
Vue.component('iconFormatQuoteClose', FormatQuoteClose);
Vue.component('iconLinkVariant', LinkVariant);
Vue.component('iconFileImage', FileImage);
Vue.component('iconTable', Table);
Vue.component('iconFormatListNumbers', FormatListNumbers);
Vue.component('iconFormatListBulleted', FormatListBulleted);
Vue.component('iconFormatSize', FormatSize);
Vue.component('iconFormatStrikethrough', FormatStrikethrough);
Vue.component('iconStatusBar', StatusBar);
Vue.component('iconNavigationBar', NavigationBar);
Vue.component('iconSidePreview', SidePreview);
Vue.component('iconEye', Eye);
Vue.component('iconSettings', Settings);
Vue.component('iconFilePlus', FilePlus);
Vue.component('iconFileMultiple', FileMultiple);
Vue.component('iconFolderPlus', FolderPlus);
Vue.component('iconDelete', Delete);
Vue.component('iconClose', Close);
Vue.component('iconPen', Pen);
Vue.component('iconTarget', Target);
Vue.component('iconArrowLeft', ArrowLeft);
Vue.component('iconHelpCircle', HelpCircle);
Vue.component('iconToc', Toc);
Vue.component('iconLogin', Login);
Vue.component('iconLogout', Logout);
Vue.component('iconSync', Sync);
Vue.component('iconSyncOff', SyncOff);
Vue.component('iconUpload', Upload);
Vue.component('iconViewList', ViewList);
Vue.component('iconDownload', Download);
Vue.component('iconCodeTags', CodeTags);
Vue.component('iconCodeBraces', CodeBraces);
Vue.component('iconOpenInNew', OpenInNew);
Vue.component('iconInformation', Information);
Vue.component('iconAlert', Alert);
Vue.component('iconSignalOff', SignalOff);
Vue.component('iconFolder', Folder);
Vue.component('iconScrollSync', ScrollSync);
Vue.component('iconPrinter', Printer);
Vue.component('iconUndo', Undo);
Vue.component('iconRedo', Redo);
Vue.component('iconContentSave', ContentSave);
Vue.component('iconMessage', Message);
Vue.component('iconHistory', History);
Vue.component('iconDatabase', Database);
Vue.component('iconMagnify', Magnify);
Vue.component('iconFormatListChecks', FormatListChecks);
Vue.component('iconCheckCircle', CheckCircle);
Vue.component('iconContentCopy', ContentCopy);
Vue.component('iconKey', Key);
Vue.component('iconDotsHorizontal', DotsHorizontal);
Vue.component('iconSeal', Seal);
