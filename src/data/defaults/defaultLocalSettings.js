export default () => ({
  welcomeFileHashes: {},
  filePropertiesTab: '',
  htmlExportTemplate: 'styledHtml',
  pdfExportTemplate: 'styledHtml',
  pandocExportFormat: 'pdf',
  googleDriveRestrictedAccess: false,
  googleDriveFolderId: '',
  googleDriveWorkspaceFolderId: '',
  googleDrivePublishFormat: 'markdown',
  googleDrivePublishTemplate: 'styledHtml',
  bloggerBlogUrl: '',
  bloggerPublishTemplate: 'plainHtml',
  dropboxRestrictedAccess: false,
  dropboxPublishTemplate: 'styledHtml',
  githubRepoFullAccess: false,
  githubRepoUrl: '',
  githubWorkspaceRepoUrl: '',
  githubPublishTemplate: 'jekyllSite',
  gistIsPublic: false,
  gistPublishTemplate: 'plainText',
  gitlabServerUrl: '',
  gitlabApplicationId: '',
  gitlabProjectUrl: '',
  gitlabWorkspaceProjectUrl: '',
  *********************: 'plainText',
  wordpressDomain: '',
  wordpressPublishTemplate: 'plainHtml',
  zendeskSiteUrl: '',
  zendeskClientId: '',
  zendescPublishSectionId: '',
  zendescPublishLocale: '',
  zendeskPublishTemplate: 'plainHtml',
});
