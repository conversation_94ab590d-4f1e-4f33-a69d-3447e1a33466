export default [{
}, {
  method: 'bold',
  title: 'Bold',
  icon: 'format-bold',
}, {
  method: 'italic',
  title: 'Italic',
  icon: 'format-italic',
}, {
  method: 'heading',
  title: 'Heading',
  icon: 'format-size',
}, {
  method: 'strikethrough',
  title: 'Strikethrough',
  icon: 'format-strikethrough',
}, {
}, {
  method: 'ulist',
  title: 'Unordered list',
  icon: 'format-list-bulleted',
}, {
  method: 'olist',
  title: 'Ordered list',
  icon: 'format-list-numbers',
}, {
  method: 'clist',
  title: 'Check list',
  icon: 'format-list-checks',
}, {
}, {
  method: 'quote',
  title: 'Blockquote',
  icon: 'format-quote-close',
}, {
  method: 'code',
  title: 'Code',
  icon: 'code-tags',
}, {
  method: 'table',
  title: 'Table',
  icon: 'table',
}, {
  method: 'link',
  title: 'Link',
  icon: 'link-variant',
}, {
  method: 'image',
  title: 'Image',
  icon: 'file-image',
}];
