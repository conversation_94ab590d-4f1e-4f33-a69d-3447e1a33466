@import './variables.scss';

body {
  background-color: #fff;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  tab-size: 4;
  text-rendering: auto;

  /* Prevent body overscroll on Chrome */
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
}

* {
  box-sizing: border-box;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar {
  background-color: transparent;

  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
  &:horizontal {
    height: 8px;
  }

  /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
  &:vertical {
    width: 8px;
  }
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #bbb;

  .app--dark & {
    background-color: #666;
  }
}

:focus {
  outline: none;
}

input[type=checkbox] {
  outline: #349be8 auto 5px;
}

.icon {
  width: 100%;
  height: 100%;
  display: block;

  * {
    fill: currentColor;
  }
}

.table-wrapper {
  max-width: 100%;
  overflow: auto;
}

button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.text-input {
  display: block;
  font-variant-ligatures: no-common-ligatures;
  width: 100%;
  height: 36px;
  padding: 3px 12px;
  font-size: inherit;
  line-height: 1.5;
  color: inherit;
  background-color: #fff;
  background-image: none;
  border: 0;
  border-radius: $border-radius-base;
}

.button {
  color: #333;
  background-color: transparent;
  display: inline-block;
  height: auto;
  padding: 8px 16px;
  font-size: 17px;
  font-weight: 400;
  line-height: 1.4;
  text-transform: uppercase;
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 0;
  border-radius: $border-radius-base;
  text-decoration: none;

  &:active,
  &:focus,
  &:hover,
  .hidden-file:focus + & {
    color: #333;
    background-color: rgba(0, 0, 0, 0.05);
    outline: 0;
    text-decoration: none;
  }

  .app--dark .layout__panel--editor &,
  .app--dark .layout__panel--preview & {
    color: #ccc;

    &:active,
    &:focus,
    &:hover {
      color: #ccc;
      background-color: rgba(255, 255, 255, 0.067);
    }
  }

  &[disabled] {
    &,
    &:active,
    &:focus,
    &:hover {
      opacity: 0.33;
      background-color: transparent;
      cursor: not-allowed;
    }
  }
}

.button--resolve {
  background-color: #349be8;
  color: #fff;
  margin: -2px 0 -2px 4px;
  padding: 10px 20px;
  font-size: 18px;

  &:active,
  &:focus,
  &:hover {
    color: #fff;
    background-color: darken(#349be8, 8%);
  }
}

.textfield {
  background-color: #fff;
  border: 0;
  font-family: inherit;
  font-weight: 400;
  font-size: 1.05em;
  padding: 0 0.6rem;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  color: inherit;
  height: 2.4rem;

  &:focus {
    outline: none;
  }

  &[disabled] {
    cursor: not-allowed;
    background-color: #f0f0f0;
    color: #999;
  }
}

.flex {
  display: flex;
}

.flex--row {
  flex-direction: row;
}

.flex--column {
  flex-direction: column;
}

.flex--center {
  justify-content: center;
}

.flex--end {
  justify-content: flex-end;
}

.flex--space-between {
  justify-content: space-between;
}

.flex--align-center {
  align-items: center;
}

.flex--align-end {
  align-items: flex-end;
}

.user-name {
  font-weight: 600;
}

.side-title {
  height: 44px;
  line-height: 36px;
  padding: 4px 4px 0;
  background-color: rgba(0, 0, 0, 0.1);
  flex: none;
}

.side-title__button {
  width: 38px;
  height: 36px;
  padding: 6px;
  display: inline-block;
  background-color: transparent;
  opacity: 0.75;
  flex: none;

  /* prevent from seeing wrapped buttons */
  margin-bottom: 20px;

  &:active,
  &:focus,
  &:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.side-title__title {
  text-transform: uppercase;
  padding: 0 5px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
}

.logo-background {
  background: no-repeat center url('../assets/logo.svg');
  background-size: contain;
}

.gutter {
  position: absolute;
  top: 0;
  height: 100%;
}

.gutter__background {
  position: absolute;
  height: 100%;
  right: 0;
}

.new-discussion-button {
  color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  padding: 3px 3px 3px 0;
  width: 22px;
  height: 21px;
  line-height: 1;

  .app--dark & {
    color: rgba(255, 255, 255, 0.33);
  }

  &:active,
  &:focus,
  &:hover {
    color: rgba(0, 0, 0, 0.4);

    .app--dark & {
      color: rgba(255, 255, 255, 0.4);
    }
  }
}

.discussion-editor-highlighting,
.discussion-preview-highlighting {
  background-color: mix($editor-background-light, $selection-highlighting-color, 70%);
  padding: 0.25em 0;

  .app--dark & {
    background-color: mix($editor-background-dark, $selection-highlighting-color, 70%);
  }
}

.discussion-editor-highlighting--hover,
.discussion-preview-highlighting--hover {
  background-color: mix($editor-background-light, $selection-highlighting-color, 50%);

  .app--dark & {
    background-color: mix($editor-background-dark, $selection-highlighting-color, 50%);
  }

  * {
    background-color: transparent;
  }
}

.discussion-editor-highlighting--selected,
.discussion-preview-highlighting--selected {
  background-color: mix($editor-background-light, $selection-highlighting-color, 20%);

  .app--dark & {
    background-color: mix($editor-background-dark, $selection-highlighting-color, 20%);
  }

  * {
    background-color: transparent;
  }
}

.discussion-preview-highlighting {
  cursor: pointer;

  &.discussion-preview-highlighting--selected {
    cursor: auto;
  }
}

.hidden-rendering-container {
  position: absolute;
  width: 500px;
  left: -1000px;
}

@media print {
  body {
    background-color: transparent !important;
    color: #000 !important; // Black prints faster
    overflow: visible !important;
    position: absolute !important;

    div {
      display: none !important;
    }

    a {
      text-decoration: underline;
    }
  }

  body > .app,
  body > .app > .layout,
  body > .app > .layout > .layout__panel,
  body > .app > .layout > .layout__panel > .layout__panel,
  body > .app > .layout > .layout__panel > .layout__panel > .layout__panel,
  body > .app > .layout > .layout__panel > .layout__panel > .layout__panel > .layout__panel--preview,
  body > .app > .layout > .layout__panel > .layout__panel > .layout__panel > .layout__panel--preview div {
    background-color: transparent !important;
    display: block !important;
    height: auto !important;
    overflow: visible !important;
    position: static !important;
    width: auto !important;
    font-size: 16px;
  }

  .preview__inner-2 {
    padding: 0 50px !important;
  }
  // scss-lint:enable ImportantRule
}
