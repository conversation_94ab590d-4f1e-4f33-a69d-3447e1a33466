<!DOCTYPE html>
<html manifest="cache.manifest">

<head>
    <title>StackEdit – In-browser Markdown editor</title>
    <link rel="canonical" href="https://stackedit.io/">
    <link rel="icon" href="static/landing/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="static/landing/favicon.ico" type="image/x-icon">
    <meta charset="UTF-8">
    <meta name="description"
          content="Full-featured, open-source Markdown editor based on PageDown, the Markdown library used by Stack Overflow and the other Stack Exchange sites.">
    <meta name="author" content="Benoit Schweblin">
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="msvalidate.01" content="5E47EE6F67B069C17E3CDD418351A612">
    <meta name="google-site-verification" content="iWDn0T2r2_bDQWp_nW23MGePbO9X0M8wQSzbOU70pFQ" />
    <link rel="stylesheet" href="https://stackedit.io/style.css">
    <style>
        body {
            background-color: #fbfbfb;
        }

        * {
            box-sizing: border-box;
        }

        h1 {
            font-weight: 400;
            text-align: center;
            font-size: 2.5em;
            margin: 2.5em 0;
        }

        h3 {
            margin: 1em 0;
        }

        .button {
            color: #555;
            font-size: 20px;
            background-color: transparent;
            display: inline-block;
            height: auto;
            padding: 6px 12px;
            margin: 0;
            font-weight: 400;
            line-height: 1.4;
            text-transform: uppercase;
            overflow: hidden;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            -ms-touch-action: manipulation;
            touch-action: manipulation;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-image: none;
            border: 0;
            border-radius: 2px;
            text-decoration: none;
        }

        .button:active,
        .button:focus,
        .button:hover {
            color: #333;
            background-color: rgba(0, 0, 0, 0.05);
            outline: 0;
            text-decoration: none;
        }

        .icon {
            width: 100%;
            height: 100%;
            display: inline;
        }

        .icon * {
            fill: currentColor;
        }

        .button .icon {
            height: 30px;
            width: 30px;
            margin: -6px 6px -6px 0;
        }

        .row {
            margin: 8em 0;
        }

        .row::after {
            display: block;
            content: '';
            clear: both;
        }

        @media (min-width: 700px) {
            .column {
                width: 50%;
                float: right;
            }
        }

        .landing {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .landing__content {
            margin-left: auto;
            margin-right: auto;
            padding-left: 30px;
            padding-right: 30px;
            max-width: 1000px;
        }

        .landing__footer {
            padding: 1em 0;
            text-align: center;
            background-color: #007acc;
            color: rgba(255, 255, 255, 0.75);
            font-size: 0.9em;
        }

        .landing__footer a {
            color: #fff;
        }

        .navigation-bar {
            background-color: #2c2c2c;
            position: fixed;
            padding: 5px;
            text-align: center;
            width: 100%;
            z-index: 1;
        }

        .navigation-bar__button {
            color: #b9b9b9;
        }

        .navigation-bar__button:active,
        .navigation-bar__button:focus,
        .navigation-bar__button:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .splash-screen {
            position: relative;
            width: 100%;
            height: 100%;
            padding: 25px;
        }

        .splash-screen__logo {
            width: 300px;
            height: 150px;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
            background: no-repeat center url('static/landing/logo.svg');
            background-size: contain;
        }

        @media (min-width: 700px) {
            .splash-screen__logo {
                width: 600px;
                height: 160px;
            }
        }

        .splash-screen__subtitle {
            position: absolute;
            text-align: center;
            color: #777;
            top: 95px;
            right: 5px;
            font-size: 16px;
        }

        @media (min-width: 700px) {
            .splash-screen__subtitle {
                text-align: right;
                top: 125px;
                font-size: 22px;
            }
        }

        .splash-screen__footer {
            position: absolute;
            bottom: 25px;
            left: 0;
            width: 100%;
            text-align: center;
        }

        .splash-screen__footer .button {
            padding: 10px 20px;
        }

        .social {
            margin: 0 5px;
        }

        .social a {
            color: #555;
            text-decoration: none;
        }

        .social a:active,
        .social a:focus,
        .social a:hover {
            color: #333;
            outline: 0;
        }

        .landing__footer .social a {
            color: rgba(255, 255, 255, 0.85);
        }

        .landing__footer .social a:active,
        .landing__footer .social a:focus,
        .landing__footer .social a:hover {
            color: #fff;
        }


        .social .icon {
            height: 30px;
            width: 30px;
        }

        .feature {
            padding: 5px 5px;
            border-radius: 2px;
            max-width: 350px;
            margin: 1em auto;
            text-align: center;
        }

        .image {
            display: block;
            margin: 1em auto;
            border: 1px solid #eee;
            border-radius: 2px;
            background-color: #fff;
        }

        .image img {
            display: block;
            margin: 0.5em auto;
        }
    </style>
    <script>
        function scrollTo(selector) {
            $('html,body').animate({scrollTop: $(selector).offset().top}, 500);
        }
    </script>
</head>

<body>
    <div class="landing">
        <div class="navigation-bar">
            <a class="navigation-bar__button button" href="app" title="The app">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 16.8363,2.73375C 16.45,2.73375 16.0688,2.88125 15.7712,3.17375L 13.6525,5.2925L 18.955,10.5962L 21.0737,8.47625C 21.665,7.89 21.665,6.94375 21.0737,6.3575L 17.895,3.17375C 17.6025,2.88125 17.2163,2.73375 16.8363,2.73375 Z M 12.9437,6.00125L 4.84375,14.1062L 7.4025,14.39L 7.57875,16.675L 9.85875,16.85L 10.1462,19.4088L 18.2475,11.3038M 4.2475,15.0437L 2.515,21.7337L 9.19875,19.9412L 8.955,17.7838L 6.645,17.6075L 6.465,15.2925"></path></svg>
                Start writing
            </a>
        </div>
        <div class="splash-screen">
            <div class="splash-screen__logo">
                <div class="splash-screen__subtitle">
                    In-browser Markdown editor

                    <div class="social">
                        <a href="https://twitter.com/stackedit" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 22.4592,6.01238C 21.6896,6.35373 20.8624,6.58442 19.9944,6.68815C 20.8803,6.15701 21.5609,5.31598 21.8813,4.31378C 21.052,4.80564 20.1336,5.16278 19.156,5.3552C 18.3732,4.52112 17.2579,4 16.0235,4C 13.6534,4 11.7317,5.92147 11.7317,8.29155C 11.7317,8.6279 11.7697,8.95546 11.8429,9.2696C 8.2761,9.0906 5.11376,7.38203 2.9971,4.78551C 2.62766,5.41935 2.41602,6.15656 2.41602,6.94309C 2.41602,8.43204 3.17365,9.74563 4.32524,10.5153C 3.6218,10.4929 2.95997,10.2999 2.3814,9.97846C 2.38099,9.99639 2.38099,10.0143 2.38099,10.0324C 2.38099,12.1118 3.86034,13.8463 5.8236,14.2406C 5.4635,14.3387 5.08435,14.3912 4.69295,14.3912C 4.41641,14.3912 4.14756,14.3642 3.88547,14.3142C 4.43162,16.0191 6.01654,17.26 7.89455,17.2945C 6.42577,18.4457 4.57528,19.1318 2.56454,19.1318C 2.21813,19.1318 1.87652,19.1114 1.54078,19.0717C 3.44004,20.2894 5.69592,21 8.11951,21C 16.0134,21 20.3302,14.4605 20.3302,8.78918C 20.3302,8.60314 20.326,8.41805 20.3177,8.23395C 21.1563,7.62886 21.8839,6.87302 22.4592,6.01238 Z "/></svg>
                        </a>
                        <a href="https://github.com/benweet/stackedit" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 11.9991,2C 6.47774,2 2.00001,6.47712 2.00001,12.0006C 2.00001,16.4184 4.86504,20.1665 8.83877,21.489C 9.33909,21.5807 9.52142,21.272 9.52142,21.007C 9.52142,20.7696 9.51282,20.1407 9.50791,19.3062C 6.72636,19.9105 6.13948,17.9657 6.13948,17.9657C 5.68459,16.8105 5.02895,16.5029 5.02895,16.5029C 4.121,15.8824 5.09771,15.895 5.09771,15.895C 6.10143,15.9657 6.62936,16.9256 6.62936,16.9256C 7.52135,18.4537 8.97014,18.0125 9.53984,17.7565C 9.63069,17.1102 9.88914,16.6696 10.1746,16.4196C 7.95415,16.1672 5.61952,15.3093 5.61952,11.4773C 5.61952,10.3856 6.00934,9.49292 6.64902,8.79388C 6.54588,8.54089 6.20271,7.52417 6.74723,6.14739C 6.74723,6.14739 7.58643,5.87851 9.49686,7.17252C 10.2943,6.95073 11.1501,6.8398 12.0003,6.83594C 12.8499,6.8398 13.7051,6.95073 14.5038,7.17252C 16.413,5.87851 17.2509,6.14739 17.2509,6.14739C 17.7967,7.52417 17.4535,8.54089 17.351,8.79388C 17.9919,9.49292 18.3787,10.3856 18.3787,11.4773C 18.3787,15.3189 16.0403,16.1642 13.8131,16.4118C 14.1717,16.7205 14.4915,17.3308 14.4915,18.2637C 14.4915,19.6005 14.4792,20.6791 14.4792,21.007C 14.4792,21.2744 14.6597,21.5855 15.1668,21.4878C 19.1374,20.1629 22,16.4172 22,12.0006C 22,6.47712 17.5223,2 11.9991,2 Z "/></svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="splash-screen__footer">
                <a class="button" href="javascript:scrollTo($('.anchor'))">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 11,4L 13,4L 13,16.0104L 18.5052,10.5052L 19.9194,11.9194L 12,19.8388L 4.08058,11.9194L 5.49479,10.5052L 11,16.0104L 11,4 Z "/></path></svg>
                    Read more
                </a>
            </div>
        </div>
        <div class="anchor"></div>
        <div class="landing__content">
            <h1>Unrivalled writing experience</h1>
            <div class="row">
                <div class="column">
                    <div class="feature">
                        <h3>Rich Markdown editor</h3>
                        <p>StackEdit’s Markdown syntax highlighting is unique. The refined text formatting of the editor helps you visualize the final rendering of your files.</p>
                    </div>
                </div>
                <div class="column">
                    <div class="image" style="width: 260px">
                        <img width="230" src="static/landing/syntax-highlighting.gif">
                    </div>
                </div>
            </div>
            <div class="row">
                <img class="image" width="410" src="static/landing/navigation-bar.png">
                <div class="feature">
                    <h3>WYSIWYG controls</h3>
                    <p>StackEdit provides very handy formatting buttons and shortcuts, thanks to PageDown, the WYSIWYG-style Markdown editor used by Stack Overflow.</p>
                </div>
            </div>
            <div class="row">
                <div class="column">
                    <div class="feature">
                        <h3>Smart layout</h3>
                        <p>Whether you write, you review, you comment… StackEdit's layout provides you with the flexibility you need, without sacrifice.</p>
                    </div>
                </div>
                <div class="column">
                    <img class="image" width="360" src="static/landing/smart-layout.png">
                </div>
            </div>
            <div class="row">
                <div class="feature">
                    <h3>Live preview with Scroll Sync</h3>
                    <p>StackEdit’s Scroll Sync feature accurately binds the scrollbars of the editor panel and the preview panel to ensure that you always keep an eye on the output while writing.</p>
                </div>
                <img class="image" width="485" src="static/landing/scroll-sync.gif">
            </div>
            <h1>Designed for web writers</h1>
            <div class="row">
                <div class="column">
                    <div class="feature">
                        <h3>Stay connected</h3>
                        <p>StackEdit can sync your files with Google Drive, Dropbox and GitHub. It can also publish them as blog posts to Blogger, WordPress and Zendesk. You can choose whether to upload in Markdown format, HTML, or to format the output using the Handlebars template engine.</p>
                    </div>
                </div>
                <div class="column">
                    <img class="image" width="300" src="static/landing/providers.png">
                </div>
            </div>
            <div class="row">
                <div class="column">
                    <div class="feature">
                        <h3>Collaborate</h3>
                        <p>With StackEdit, you can share collaborative workspaces, thanks to the synchronization mechanism. If two collaborators are working on the same file at the same time, StackEdit takes care of merging the changes.</p>
                    </div>
                    <img class="image" width="300" src="static/landing/workspace.png">
                </div>
                <div class="column">
                    <div class="feature">
                        <h3>Comment</h3>
                        <p>StackEdit allows you to insert inline comments and embed collaborator discussions in your files, just as well as Microsoft Word and Google Docs.</p>
                    </div>
                    <img class="image" width="395" src="static/landing/discussion.png">
                </div>
            </div>
            <div class="row">
                <div class="feature">
                    <h3>Write offline!</h3>
                    <p>Even when you travel, StackEdit is still accessible and lets you write offline just like any desktop application. You have no excuse!</p>
                </div>
            </div>
            <h1>Extended Markdown support</h1>
            <div class="row">
                <div class="column">
                    <br>
                    <div class="image" style="width: 250px">
                        <img width="230" src="static/landing/gfm.png">
                    </div>
                </div>
                <div class="column">
                    <div class="feature">
                        <h3>GitHub Flavored Markdown</h3>
                        <p>StackEdit supports different Markdown flavors such as Markdown Extra, GFM and CommonMark. Each Markdown feature can be enabled or disabled at your convenience.</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="column">
                    <br>
                    <div class="image" style="width: 270px">
                        <img width="250" src="static/landing/katex.gif">
                    </div>
                </div>
                <div class="column">
                    <div class="feature">
                        <h3>LaTeX mathematical expressions</h3>
                        <p>StackEdit renders mathematics from LaTeX expressions inside your markdown file, as you would do on Stack Exchange.</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="column">
                    <div class="image" style="width: 300px">
                        <img width="280" src="static/landing/mermaid.gif">
                    </div>
                </div>
                <div class="column">
                    <div class="feature">
                        <h3>UML diagrams</h3>
                        <p>StackEdit enables you to write sequence diagrams and flow charts using a simple syntax.</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="column">
                    <div class="image" style="width: 280px">
                        <img width="260" src="static/landing/abc.png">
                    </div>
                </div>
                <div class="column">
                    <div class="feature">
                        <h3>Scores</h3>
                        <p>StackEdit can render musical scores using the ABC notation.</p>
                    </div>
                </div>
            </div>
            <div class="row">
              <div class="column">
                <div class="image" style="width: 250px">
                  <img width="230" src="static/landing/twemoji.png">
                </div>
              </div>
              <div class="column">
                <div class="feature">
                  <h3>Emojis</h3>
                  <p>StackEdit supports inserting emojis in your file using the Markdown emoji markup.</p>
                </div>
              </div>
            </div>
        </div>
        <div class="landing__footer">
            <div class="social">
                <a href="https://twitter.com/stackedit" target="_blank">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 22.4592,6.01238C 21.6896,6.35373 20.8624,6.58442 19.9944,6.68815C 20.8803,6.15701 21.5609,5.31598 21.8813,4.31378C 21.052,4.80564 20.1336,5.16278 19.156,5.3552C 18.3732,4.52112 17.2579,4 16.0235,4C 13.6534,4 11.7317,5.92147 11.7317,8.29155C 11.7317,8.6279 11.7697,8.95546 11.8429,9.2696C 8.2761,9.0906 5.11376,7.38203 2.9971,4.78551C 2.62766,5.41935 2.41602,6.15656 2.41602,6.94309C 2.41602,8.43204 3.17365,9.74563 4.32524,10.5153C 3.6218,10.4929 2.95997,10.2999 2.3814,9.97846C 2.38099,9.99639 2.38099,10.0143 2.38099,10.0324C 2.38099,12.1118 3.86034,13.8463 5.8236,14.2406C 5.4635,14.3387 5.08435,14.3912 4.69295,14.3912C 4.41641,14.3912 4.14756,14.3642 3.88547,14.3142C 4.43162,16.0191 6.01654,17.26 7.89455,17.2945C 6.42577,18.4457 4.57528,19.1318 2.56454,19.1318C 2.21813,19.1318 1.87652,19.1114 1.54078,19.0717C 3.44004,20.2894 5.69592,21 8.11951,21C 16.0134,21 20.3302,14.4605 20.3302,8.78918C 20.3302,8.60314 20.326,8.41805 20.3177,8.23395C 21.1563,7.62886 21.8839,6.87302 22.4592,6.01238 Z "/></svg>
                </a>
                <a href="https://github.com/benweet/stackedit" target="_blank">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="icon"><path d="M 11.9991,2C 6.47774,2 2.00001,6.47712 2.00001,12.0006C 2.00001,16.4184 4.86504,20.1665 8.83877,21.489C 9.33909,21.5807 9.52142,21.272 9.52142,21.007C 9.52142,20.7696 9.51282,20.1407 9.50791,19.3062C 6.72636,19.9105 6.13948,17.9657 6.13948,17.9657C 5.68459,16.8105 5.02895,16.5029 5.02895,16.5029C 4.121,15.8824 5.09771,15.895 5.09771,15.895C 6.10143,15.9657 6.62936,16.9256 6.62936,16.9256C 7.52135,18.4537 8.97014,18.0125 9.53984,17.7565C 9.63069,17.1102 9.88914,16.6696 10.1746,16.4196C 7.95415,16.1672 5.61952,15.3093 5.61952,11.4773C 5.61952,10.3856 6.00934,9.49292 6.64902,8.79388C 6.54588,8.54089 6.20271,7.52417 6.74723,6.14739C 6.74723,6.14739 7.58643,5.87851 9.49686,7.17252C 10.2943,6.95073 11.1501,6.8398 12.0003,6.83594C 12.8499,6.8398 13.7051,6.95073 14.5038,7.17252C 16.413,5.87851 17.2509,6.14739 17.2509,6.14739C 17.7967,7.52417 17.4535,8.54089 17.351,8.79388C 17.9919,9.49292 18.3787,10.3856 18.3787,11.4773C 18.3787,15.3189 16.0403,16.1642 13.8131,16.4118C 14.1717,16.7205 14.4915,17.3308 14.4915,18.2637C 14.4915,19.6005 14.4792,20.6791 14.4792,21.007C 14.4792,21.2744 14.6597,21.5855 15.1668,21.4878C 19.1374,20.1629 22,16.4172 22,12.0006C 22,6.47712 17.5223,2 11.9991,2 Z "/></svg>
                </a>
            </div>
            <a href="app" title="The app">The app</a> – <a href="https://community.stackedit.io" target="_blank" title="The app">Community</a><br>
            Copyright 2013-2019 <a href="https://twitter.com/benweet" target="_blank">Benoit Schweblin</a><br>
            Licensed under an
            <a target="_blank" href="http://www.apache.org/licenses/LICENSE-2.0">Apache License</a> –
            <a href="privacy_policy.html" target="_blank">Privacy Policy</a>
        </div>
    </div>
    <script src="https://code.jquery.com/jquery-2.2.4.min.js" integrity="sha256-BbhdlvQf/xTY9gja0Dq3HiwQF8LaCRTXxZKRutelT44=" crossorigin="anonymous"></script>
</body>

</html>
